# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\flutter\\commercial app\\commercial" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\flutter"
  "PROJECT_DIR=C:\\flutter\\commercial app\\commercial"
  "FLUTTER_ROOT=C:\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\flutter\\commercial app\\commercial\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\flutter\\commercial app\\commercial"
  "FLUTTER_TARGET=C:\\flutter\\commercial app\\commercial\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzEuMC0wLjEucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNlODA3NTMwYQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049NTBmMjI2NTY5Zg==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjAgKGJ1aWxkIDMuOC4wLTE3MS4wLmRldik="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\flutter\\commercial app\\commercial\\.dart_tool\\package_config.json"
)
