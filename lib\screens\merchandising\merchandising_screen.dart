import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../providers/auth_provider.dart';

import '../../widgets/vitabrosse_logo.dart';
import 'calendrier_missions_screen.dart';
import 'rapports_merchandisers_screen.dart';

class MerchandisingScreen extends StatefulWidget {
  const MerchandisingScreen({super.key});

  @override
  State<MerchandisingScreen> createState() => _MerchandisingScreenState();
}

class _MerchandisingScreenState extends State<MerchandisingScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  bool _isCommercialUser = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Check user type and initialize tab controller accordingly
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      _isCommercialUser = authProvider.userType == 'commercial';
      _isInitialized = true;

      // Initialize tab controller with appropriate length
      _tabController =
          TabController(length: _isCommercialUser ? 2 : 3, vsync: this);

      _chargerDonnees();

      // Trigger rebuild to update UI
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _chargerDonnees() async {
    final merchandiserProvider = Provider.of<MerchandiserProvider>(
      context,
      listen: false,
    );

    await merchandiserProvider.chargerMerchandisers();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    // Show loading indicator if TabController is not initialized yet
    if (!_isInitialized || _tabController == null) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
          ),
        ),
      );
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  VitaBrosseLogo(
                    height: isSmallScreen ? 24 : 28,
                    showText: false,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Merchandising',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 20,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Gestion terrain et visites',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF10B981).withValues(alpha: 0.08),
                      const Color(0xFF059669).withValues(alpha: 0.08),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    // Décoration de fond adaptée
                    Positioned(
                      top: isSmallScreen ? 20 : 40,
                      right: isSmallScreen ? 10 : 20,
                      child: Container(
                        width: isSmallScreen ? 60 : 100,
                        height: isSmallScreen ? 60 : 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF10B981).withValues(alpha: 0.15),
                              const Color(0xFF059669).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: isSmallScreen ? 30 : 60,
                      right: isSmallScreen ? 20 : 40,
                      child: Container(
                        width: isSmallScreen ? 40 : 60,
                        height: isSmallScreen ? 40 : 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF059669).withValues(alpha: 0.2),
                              const Color(0xFF10B981).withValues(alpha: 0.15),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.store,
                            size: isSmallScreen ? 20 : 28,
                            color: const Color(0xFF059669),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(48.0),
              child: Container(
                color: Colors.white,
                child: TabBar(
                  controller: _tabController!,
                  indicatorColor: const Color(0xFF10B981),
                  indicatorWeight: 3,
                  labelColor: const Color(0xFF10B981),
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: isSmallScreen ? 12 : 14,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: isSmallScreen ? 12 : 14,
                  ),
                  isScrollable: isSmallScreen,
                  tabs: _isCommercialUser
                      ? [
                          Tab(text: 'Calendrier'),
                          Tab(text: 'Rapports'),
                        ]
                      : [
                          Tab(text: isSmallScreen ? 'Vue' : 'Vue d\'ensemble'),
                          Tab(text: 'Calendrier'),
                          Tab(text: 'Rapports'),
                        ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController!,
              children: _isCommercialUser
                  ? [
                      CalendrierMissionsScreen(),
                      const RapportsMerchandisersScreen(),
                    ]
                  : [
                      MerchandisingDashboard(isSmallScreen: isSmallScreen),
                      CalendrierMissionsScreen(),
                      const RapportsMerchandisersScreen(),
                    ],
            ),
          ),
        ],
      ),
    );
  }
}

class MerchandisingDashboard extends StatelessWidget {
  final bool isSmallScreen;

  const MerchandisingDashboard({super.key, required this.isSmallScreen});
  @override
  Widget build(BuildContext context) {
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Vue d\'ensemble',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2937),
                  fontSize: isSmallScreen ? 18 : 22,
                ),
          ),
          SizedBox(height: isSmallScreen ? 16 : 20),

          // Statistiques rapides adaptées au mobile
          Expanded(
            child: GridView.count(
              crossAxisCount: isSmallScreen ? 2 : 2,
              crossAxisSpacing: isSmallScreen ? 12 : 16,
              mainAxisSpacing: isSmallScreen ? 12 : 16,
              childAspectRatio: isSmallScreen ? 1.1 : 1.2,
              children: [
                _buildStatCard(
                  context,
                  'Merchandisers',
                  Icons.people_outline,
                  const Color(0xFF3B82F6),
                  Consumer<MerchandiserProvider>(
                    builder: (context, provider, child) {
                      return FutureBuilder<Map<String, dynamic>>(
                        future: provider.obtenirStatistiques(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return _buildCardContent(
                              '${snapshot.data!['nombreActifs']}',
                              'Actifs',
                              isSmallScreen,
                            );
                          }
                          return const CircularProgressIndicator();
                        },
                      );
                    },
                  ),
                  isSmallScreen,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    Widget valueWidget,
    bool isSmallScreen,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: isSmallScreen ? 16 : 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: isSmallScreen ? 20 : 24, color: color),
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                  fontSize: isSmallScreen ? 12 : 14,
                ),
          ),
          SizedBox(height: isSmallScreen ? 2 : 4),
          valueWidget,
        ],
      ),
    );
  }

  Widget _buildCardContent(String value, String subtitle, bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF1F2937),
          ),
        ),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: isSmallScreen ? 10 : 12,
            color: Colors.grey.shade500,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
